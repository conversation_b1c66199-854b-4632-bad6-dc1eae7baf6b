import{u as Z,o as V,d as k,r as o,e as C,D as R,j as e,E as W,H as y,I as B,J as b}from"./index-CCwEMTeA.js";const z="data:image/png;base64,aVZCT1J3MEtHZ29BQUFBTlNVaEVVZ0FBQURBQUFBQXdDQVlBQUFCWEF2bUhBQUFBQ1hCSVdYTUFBQXNUQUFBTEV3RUFtcHdZQUFBRHNVbEVRVlI0bk8yWlRXaGNWUlRIZjIvU3BFM1RKazBTSjAzVE5rMGIrNVdQMmxaYlA0SllYQWlDQ3hldUJEZUNDeGV1WExod0piZ1JYQWl1eElVTFFSQkVFTUdGSUlnZ0loUkVFRVFRUVFRUlJCQkJCQkZFRU9FMUozbkRNSmw1SC9QZW01bEEvbkNZTy9majNQKzU1NTV6N3owRExiWFVVak9xRFJ3R3pnTlhnUnZBYmVBZWNBLzRHL2dOK0FuNEZ2Z0NPQU1jQUhZMFllUlR3QWZBYjBBVXFLK0JqNEduRzBqOEtQQU44RytnaEwzNkIvZ2FlS1FPeFBjQkh3RjNJeVR1MVIzZ1EyQnZIY2gvRlRIaHN2b0JPRkFoK2RlQlB5c2s3OVFmd0dzVmtYOFIrS3RHOGs3OUNieFFBZmtYZ2I5ckp1L1VMZURGa3NtL0FmelRBSG1uL2daT2xraitWUHowbXlMdjFHM2dwWkxJdjlVZytZbmtYeTZCL0xzTmszZnF2UkxJbjIyWXVGUG5TaUIvdm1IaVRsMG9nZnlYRFJOMzZxc1N5SC9kTUhHbnZpK0IvSThORTNmcVNnbmtmMm1ZdUZQWFNpQi92V0hpVHQwc2dmeGZEUk4zNm5ZSjVPODFUTnlwZjBzZ1AycVl1Rk9qRXNpUEd5YnUxTGdFOHBPR2lUczFLWUg4dEdIaVRrMUxJTjlxbUxoVHJSTEl0Mk1sWGpmNWRnbmsyekZUcjVOOHV3VHk3WmpKMTBXK1hRTDVkc3pFNnlEZkxvRjhPM2J5VlpOdmwwQytIVHZ4S3NtM1N5RGZqcDE0VmVUYkpaQnZ4MDY4Q3ZMdEVzaTNZeWRlTnZsMkNlVGJzUk12azN5N0JQTHQySW1YUmI1ZEF2bDI3TVRMSU44dWdYdzdkdUpGazIrWFFMNGRPL0VpeWJkTElOK09uWGhSNU5zbGtHL0hUcndJOHUwU3lMZGpKNTZYZkxzRTh1M1lpZWNaZXpzRytYYnN4UE9NdlIyRGZEdDI0bm5HM281QnZoMDc4VHhqYjhjZzM0NmRlSjZ4dDJPUWI4ZE9QTS9ZMnpISXQyTW5ubWZzYlFQeFBjRG53TSt4RTg4ejlyYUIrQ0hnWXVURTg0eTliU0IrQkxnVU9mRThZMjhiaUI4RHJrUk9QTS9ZMndiaUo0Q2JrUlBQTS9hMmdmZ3A0RTdreFBPTXZXMGdmZ2E0SHpueFBHTnZHNGlmQlI1RVRqelAyTnNHNHU4QWs4aUo1eGw3MjBEOGZlQmg1TVR6akwxdElINEJtRVZPUE0vWTJ3YmlId0tMeUlubkdYdmJRUHdUWUJrNThUeGpiMWNnL2xua3hQT012VjJCK0plUkU4OHo5bllGNGw5SFRqelAyTnNWaUg4Yk9mRThZMjlYSVA1RDVNVHpqTDFkZ2ZqbHlJbm5HWHU3QXZHcmtSUFBNL1oyQmVJM0lpZnUwL1VLeE4rcVFQenR5SW5uMFowS3hOK3RRUHhlNU1SOXVsK0IrSU1LeEI5R1R0eW5oMVZzb0ZRZ3ZveWNlSjVObEFyRVY1RVQ5MmxkeFFaS0JlS2J5SW43dEtsaUE2VUM4VTNreEgzYVVyR0JVb0g0Tm5MaVB1Mm8yRUNwUUh3bk9YR2ZkbFZzb0ZRZ3ZwdWN1RTk3S2paUUtoRGZTMDdjcDMwVkd5Z3RWYUwvQVlyODM5NlIzQkh3QUFBQUFFbEZUa1N1UW1DQwo=",T="data:image/svg+xml,%3csvg%20width='56'%20height='56'%20viewBox='0%200%2056%2056'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M54.0284%2027.9988C54.8932%2026.9917%2055.4993%2025.7888%2055.794%2024.4944C56.0887%2023.2%2056.0631%2021.8533%2055.7195%2020.5711C55.3759%2019.2888%2054.7246%2018.1097%2053.8222%2017.1362C52.9197%2016.1626%2051.7934%2015.4239%2050.5408%2014.9842C50.7863%2013.6795%2050.7096%2012.3347%2050.3176%2011.0663C49.9256%209.79797%2049.23%208.64442%2048.2912%207.70576C47.3525%206.76709%2046.1988%206.07167%2044.9304%205.67982C43.662%205.28796%2042.3172%205.21151%2041.0125%205.4571C40.5732%204.20426%2039.8346%203.07759%2038.861%202.17493C37.8874%201.27226%2036.7082%200.620887%2035.4258%200.277349C34.1433%20-0.0661901%2032.7964%20-0.0915056%2031.502%200.2036C30.2075%200.498705%2029.0047%201.10531%2027.9979%201.97075C26.9908%201.10591%2025.7878%200.499847%2024.4935%200.20515C23.1991%20-0.089548%2021.8523%20-0.0639829%2020.5701%200.279626C19.2878%200.623235%2018.1088%201.27451%2017.1352%202.17695C16.1616%203.0794%2015.4229%204.20576%2014.9832%205.45831C13.6786%205.21312%2012.3339%205.28991%2011.0657%205.68203C9.79756%206.07415%208.64419%206.76975%207.70568%207.7085C6.76717%208.64725%206.07186%209.8008%205.68007%2011.0691C5.28827%2012.3374%205.21182%2013.6821%205.45735%2014.9866C4.20479%2015.4263%203.07843%2016.165%202.17599%2017.1386C1.27354%2018.1122%200.622264%2019.2912%200.278655%2020.5735C-0.0649546%2021.8557%20-0.0905234%2023.2025%200.204174%2024.4968C0.498872%2025.7912%201.10494%2026.9941%201.96979%2028.0013C1.10468%2029.0084%200.498423%2030.2114%200.203641%2031.5059C-0.0911404%2032.8004%20-0.0655494%2034.1473%200.27821%2035.4296C0.62197%2036.712%201.2735%2037.8911%202.17625%2038.8646C3.07901%2039.8381%204.20572%2040.5766%205.45857%2041.0159C5.21283%2042.3205%205.28919%2043.6653%205.68103%2044.9336C6.07287%2046.202%206.76836%2047.3555%207.7071%2048.2942C8.64585%2049.2328%209.79949%2049.9281%2011.0679%2050.3198C12.3363%2050.7115%2013.6811%2050.7877%2014.9857%2050.5418C15.4254%2051.7943%2016.1641%2052.9207%2017.1376%2053.8231C18.1112%2054.7256%2019.2903%2055.3769%2020.5725%2055.7205C21.8548%2056.0641%2023.2015%2056.0896%2024.4959%2055.795C25.7902%2055.5003%2026.9932%2054.8942%2028.0003%2054.0293C29.0074%2054.8945%2030.2104%2055.5007%2031.5049%2055.7955C32.7994%2056.0903%2034.1463%2056.0647%2035.4287%2055.7209C36.7111%2055.3772%2037.8902%2054.7256%2038.8637%2053.8229C39.8371%2052.9201%2040.5756%2051.7934%2041.015%2050.5406C42.3196%2050.7862%2043.6644%2050.7098%2044.9328%2050.318C46.2011%2049.9261%2047.3547%2049.2307%2048.2934%2048.292C49.2321%2047.3533%2049.9276%2046.1997%2050.3195%2044.9313C50.7113%2043.6629%2050.7877%2042.3181%2050.542%2041.0135C51.7947%2040.5739%2052.9211%2039.8352%2053.8236%2038.8616C54.7261%2037.888%2055.3774%2036.7088%2055.7209%2035.4265C56.0644%2034.1442%2056.0898%2032.7974%2055.7948%2031.503C55.4999%2030.2086%2054.8935%2029.0058%2054.0284%2027.9988Z'%20fill='url(%23paint0_linear_291_925)'/%3e%3cpath%20d='M22.9725%2038.6872L15.1916%2030.9112C14.8963%2030.6154%2014.7305%2030.2145%2014.7305%2029.7966C14.7305%2029.3786%2014.8963%2028.9778%2015.1916%2028.682L16.1369%2027.7355C16.4327%2027.4402%2016.8336%2027.2743%2017.2515%2027.2743C17.6695%2027.2743%2018.0703%2027.4402%2018.3661%2027.7355L24.0222%2033.3879L37.3426%2019.2326C37.6293%2018.9284%2038.0248%2018.7505%2038.4425%2018.7377C38.8603%2018.725%2039.266%2018.8785%2039.5706%2019.1646L40.5414%2020.0808C40.8459%2020.3675%2041.0241%2020.7633%2041.0368%2021.1813C41.0496%2021.5993%2040.8958%2022.0053%2040.6093%2022.31L25.2393%2038.652C25.0946%2038.8062%2024.9203%2038.9298%2024.7269%2039.0153C24.5335%2039.1009%2024.3248%2039.1466%2024.1134%2039.1499C23.9019%2039.1532%2023.6919%2039.1139%2023.4959%2039.0344C23.2999%2038.9549%2023.1219%2038.8369%2022.9725%2038.6872Z'%20fill='white'/%3e%3cdefs%3e%3clinearGradient%20id='paint0_linear_291_925'%20x1='27.9994'%20y1='-0.000457764'%20x2='27.9994'%20y2='55.9991'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23EE3425'/%3e%3cstop%20offset='1'%20stop-color='%23FB5024'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e",J=()=>{const x=Z(),c=V(),i=k(),[d,n]=o.useState(!1),{order:a,isLoading:S,downloadUrl:r}=C(l=>l.order),s=(()=>{var l,t,m,N,h,U,j,u,A;return(l=c.state)!=null&&l.orderData?c.state.orderData:a?{orderId:`#${((t=a._id)==null?void 0:t.slice(-8))||"12345678"}`,date:new Date(a.createdAt||Date.now()).toLocaleDateString("en-US",{day:"numeric",month:"short",year:"numeric"}),time:new Date(a.createdAt||Date.now()).toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0}),items:1,totalAmount:`$${a.amount||"20.00"}`,customerDetails:{name:(m=a.buyer)!=null&&m.firstName&&((N=a.buyer)!=null&&N.lastName)?`${a.buyer.firstName} ${a.buyer.lastName}`:"John Smith",email:((h=a.buyer)==null?void 0:h.email)||"<EMAIL>",phone:((U=a.buyer)==null?void 0:U.mobile)||"************"},paymentDetails:{method:a.paymentMethod||"Mastercard",cardNumber:"**** **** **** 1234"},itemInfo:{title:((j=a.content)==null?void 0:j.title)||"Frank Martin - Drills and Coaching Philosophies to Developing Toughness in Your Players",category:((u=a.content)==null?void 0:u.category)||"Basketball Coaching Core",image:((A=a.content)==null?void 0:A.thumbnail)||"https://via.placeholder.com/80x80/f0f0f0/666666?text=Product"}}:{orderId:"#12345678",date:"20 May 2025",time:"4:50PM",items:1,totalAmount:"$20.00",customerDetails:{name:"John Smith",email:"<EMAIL>",phone:"************"},paymentDetails:{method:"Mastercard",cardNumber:"**** **** **** 1234"},itemInfo:{title:"Frank Martin ",category:"Basketball Coaching Core",image:"data:image/jpeg;base64,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"}}})();o.useEffect(()=>{window.scrollTo(0,0);const t=new URLSearchParams(c.search).get("orderId");t&&!a&&i(R(t))},[i,c.search,a]),o.useEffect(()=>{if(r){const l=document.createElement("a");l.href=r,l.download=`order-${s.orderId}-content`,document.body.appendChild(l),l.click(),document.body.removeChild(l),n(!1)}},[r,s.orderId]);const p=async()=>{if(!d){n(!0);try{const l=(a==null?void 0:a._id)||s.orderId.replace("#","");await i(b(l))}catch(l){console.error("Download failed:",l),n(!1)}}},Q=()=>{x("/")};return e.jsx("div",{className:"thank-you-page",children:e.jsxs("div",{className:"thank-you-container max-container",children:[e.jsxs("div",{className:"success-header",children:[e.jsx("div",{className:"success-icon",children:e.jsx("img",{src:T,alt:"thankyou"})}),e.jsx("h1",{className:"success-title",children:"Congratulation for your order!"}),e.jsx("p",{className:"success-message",children:"We will update you for the delivery status soon via Email or SMS."})]}),e.jsxs("div",{className:"order-info-card",children:[e.jsx("h2",{className:"order-info-title",children:"Order Information"}),e.jsxs("div",{className:"order-details-grid",children:[e.jsxs("div",{className:"order-details-grid-container",children:[e.jsxs("div",{className:"order-detail-item",children:[e.jsx("span",{className:"detail-label",children:"Order Id:"}),e.jsx("span",{className:"detail-value",children:s.orderId})]}),e.jsxs("div",{className:"order-detail-item",children:[e.jsx("span",{className:"detail-label",children:"Items:"}),e.jsx("span",{className:"detail-value",children:s.items})]})]}),e.jsx("div",{className:"vertical-line"}),e.jsxs("div",{className:"order-details-grid-container",children:[e.jsxs("div",{className:"order-detail-item",children:[e.jsx("span",{className:"detail-label",children:"Date:"}),e.jsxs("span",{className:"detail-value",children:[s.date," | ",s.time]})]}),e.jsxs("div",{className:"order-detail-item",children:[e.jsx("span",{className:"detail-label",children:"Total Amount:"}),e.jsx("span",{className:"detail-value",children:s.totalAmount})]})]})]}),e.jsxs("div",{className:"details-section",children:[e.jsxs("div",{className:"customer-details",children:[e.jsx("h3",{className:"section-title",children:"Customer Details"}),e.jsxs("div",{className:"detail-group",children:[e.jsxs("div",{className:"detail-row",children:[e.jsx("span",{className:"detail-label",children:"Name:"}),e.jsx("span",{className:"detail-value",children:s.customerDetails.name})]}),e.jsxs("div",{className:"detail-row",children:[e.jsx("span",{className:"detail-label",children:"Email Address:"}),e.jsx("span",{className:"detail-value",children:s.customerDetails.email})]}),e.jsxs("div",{className:"detail-row",children:[e.jsx("span",{className:"detail-label",children:"Phone Number:"}),e.jsx("span",{className:"detail-value",children:s.customerDetails.phone})]})]})]}),e.jsx("div",{className:"vertical-line"}),e.jsxs("div",{className:"payment-details",children:[e.jsx("h3",{className:"section-title",children:"Payment Details"}),e.jsxs("div",{className:"payment-method",children:[e.jsx("img",{src:z,alt:"Mastercard",className:"payment-logo"}),e.jsx("span",{className:"card-number",children:s.paymentDetails.cardNumber})]})]})]}),e.jsxs("div",{className:"item-info-section",children:[e.jsx("h3",{className:"section-title",children:"Item Info"}),e.jsxs("div",{className:"item-info-content",children:[e.jsx("div",{className:"item-image",children:e.jsx("img",{src:s.itemInfo.image,alt:s.itemInfo.title,className:"product-image"})}),e.jsxs("div",{className:"item-details",children:[e.jsx("h4",{className:"item-title",children:s.itemInfo.title}),e.jsxs("p",{className:"item-category",children:["By ",s.itemInfo.category]})]})]})]}),e.jsxs("div",{className:"action-buttons",children:[e.jsxs("button",{className:"btn download-btn",onClick:p,disabled:d,children:[d?e.jsx(W,{className:"btn-icon spinning"}):e.jsx(y,{className:"btn-icon"}),d?"Downloading...":"Download"]}),e.jsxs("button",{className:"btn homepage-btn",onClick:Q,children:[e.jsx(B,{className:"btn-icon"}),"Go to Homepage"]})]})]})]})})};export{J as default};
